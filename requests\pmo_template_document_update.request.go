package requests

import (
	"strings"

	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
)

type PMOTemplateDocumentUpdate struct {
	core.BaseValidator
	TabKey        *string `json:"tab_key"`
	Name          *string `json:"name"`
	SharepointURL *string `json:"sharepoint_url"`
}

func (r *PMOTemplateDocumentUpdate) Valid(ctx core.IContext) core.IError {
	r.Must(r.IsStrIn(r.Tab<PERSON>ey, strings.Join(models.PMOTabKeys, "|"), "tab_key"))
	r.Must(r.IsURL(r.SharepointURL, "sharepoint_url"))
	return r.<PERSON>rror()
}
